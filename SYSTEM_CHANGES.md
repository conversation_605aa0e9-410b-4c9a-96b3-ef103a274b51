# System Architecture Changes

## Overview

The Bitcoin prediction system has been restructured to use CSV files instead of a backend API for serving prediction data.

## Previous Architecture

```
AI Model → Backend API Server → Frontend (HTTP requests) → ECharts Display
```

## New Architecture

```
AI Model → CSV File Generation → Frontend (CSV reading) → ECharts Display
```

## Key Changes

### 1. AI Model Updates
- **File**: `AI/bitcoin_price_prediction_using_lstm.py`
- **Changes**: 
  - Added `generate_and_save_predictions()` function
  - Generates 5-year predictions (1825 days) after model training
  - Saves predictions to `Data/bitcoin_5year_predictions.csv`
  - Automatically copies CSV to `Frontend/public/` for web access

### 2. Frontend Updates
- **New API Service**: `Frontend/src/api/csvPredictionApi.ts`
  - Reads CSV data directly from public folder
  - Supports date range filtering
  - No HTTP server dependencies

- **New Date Picker**: `Frontend/src/components/CustomDatePicker.tsx`
  - Custom date range selection (min: next 1 day, max: next 3 years)
  - Quick select buttons for common timeframes
  - Input validation and error handling

- **Updated Chart Component**: `Frontend/src/components/BitcoinPriceChart.tsx`
  - Supports both predefined timeframes and custom date ranges
  - Loads all prediction data on mount
  - Filters data client-side for better performance

### 3. Removed Components
- **Deleted**: `Backend/` folder (entire backend infrastructure)
- **Deleted**: Old API files:
  - `Frontend/src/api/predictionApi.ts`
  - `Frontend/src/api/directApiTest.ts`
  - `Frontend/src/api/mockPredictionApi.ts`
  - `Frontend/flask_test_server.py`
  - `Frontend/src/flask_api_debug.txt`

### 4. Updated Dependencies
- **Added**: `papaparse` and `@types/papaparse` for CSV parsing
- **Removed**: `proxy` setting from `package.json`

## How to Use the New System

### Step 1: Generate Predictions
```bash
cd AI
python bitcoin_price_prediction_using_lstm.py
```

This will:
1. Train the LSTM model (if not already trained)
2. Generate 5-year predictions
3. Save to `Data/bitcoin_5year_predictions.csv`
4. Copy to `Frontend/public/bitcoin_5year_predictions.csv`

### Step 2: Install Frontend Dependencies
```bash
cd Frontend
npm install
```

### Step 3: Start the Frontend
```bash
npm start
```

The application will now:
1. Load prediction data from the CSV file
2. Display predefined timeframes (1M, 6M, 1Y, 3Y)
3. Allow custom date range selection

## Features

### Predefined Timeframes
- **1M**: First 30 days of predictions
- **6M**: First 180 days of predictions  
- **1Y**: First 365 days of predictions
- **3Y**: First 1095 days of predictions (maximum allowed)

### Custom Date Range
- **Minimum**: Next 1 day from current date
- **Maximum**: Up to 3 years in the future
- **Quick Select**: Buttons for 1 week, 1 month, 3 months, 6 months, 1 year, 2 years, 3 years
- **Manual Selection**: Date pickers with validation

### Data Constraints
- Maximum prediction range: 3 years (as requested)
- CSV contains 5 years of data (allowing flexibility)
- All dates are future predictions from the last historical data point

## Benefits of New Architecture

1. **Simplified Deployment**: No backend server required
2. **Better Performance**: Client-side data filtering
3. **Offline Capability**: Works without active server connection
4. **Easier Maintenance**: No API endpoint management
5. **Cost Effective**: No server hosting costs
6. **Data Persistence**: Predictions persist until regenerated

## Troubleshooting

### CSV File Not Found
1. Run the AI model to generate predictions
2. Ensure the CSV is copied to `Frontend/public/`
3. Check browser console for specific error messages

### Date Range Issues
- Ensure selected dates are in the future
- Maximum range is 3 years from start date
- Start date must be after end date

### Performance Issues
- Large date ranges may take longer to process
- Consider using predefined timeframes for better performance

## Development Notes

- The CSV file should be regenerated periodically with fresh historical data
- Frontend automatically handles date parsing and validation
- All monetary values are formatted with proper currency symbols
- Chart tooltips show formatted dates and prices 