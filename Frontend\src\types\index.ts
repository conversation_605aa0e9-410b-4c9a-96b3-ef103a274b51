export interface PredictionResponse {
  [key: string]: {
    dates: string[];
    predicted_prices: number[];
  };
}

export type TimeFrame = '1m' | '6m' | '1y' | '3y' | 'custom';

export interface TimeFrameMapping {
  [key: string]: number;
}

export interface ChartData {
  dates: string[];
  prices: number[];
}

export interface DateRange {
  startDate: Date;
  endDate: Date;
}

export interface CustomDateRangeProps {
  onDateRangeChange: (dateRange: DateRange) => void;
  minDate: Date;
  maxDate: Date;
  defaultStartDate?: Date;
  defaultEndDate?: Date;
} 