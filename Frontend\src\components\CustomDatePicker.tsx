import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { CustomDateRangeProps, DateRange } from '../types';

const DatePickerContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 20px;
  background: linear-gradient(135deg, #1e2328 0%, #2b2f36 100%);
  border-radius: 12px;
  border: 1px solid #30363d;
  margin-bottom: 20px;
`;

const DatePickerRow = styled.div`
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
`;

const DateInputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  color: #e6e8ea;
  font-size: 14px;
  font-weight: 500;
`;

const DateInput = styled.input`
  padding: 10px 12px;
  border: 1px solid #30363d;
  border-radius: 8px;
  background: #21262d;
  color: #e6e8ea;
  font-size: 14px;
  min-width: 140px;
  
  &:focus {
    outline: none;
    border-color: #f7931a;
    box-shadow: 0 0 0 2px rgba(247, 147, 26, 0.2);
  }
  
  &::-webkit-calendar-picker-indicator {
    filter: invert(1);
    cursor: pointer;
  }
`;

const ApplyButton = styled.button`
  padding: 10px 20px;
  background: linear-gradient(135deg, #f7931a 0%, #ff8c00 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(247, 147, 26, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    background: #30363d;
    color: #7d8590;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const QuickSelectContainer = styled.div`
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
`;

const QuickSelectButton = styled.button<{ isActive: boolean }>`
  padding: 8px 12px;
  background: ${props => props.isActive 
    ? 'linear-gradient(135deg, #f7931a 0%, #ff8c00 100%)' 
    : 'transparent'};
  color: ${props => props.isActive ? 'white' : '#e6e8ea'};
  border: 1px solid ${props => props.isActive ? '#f7931a' : '#30363d'};
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #f7931a;
    background: ${props => props.isActive 
      ? 'linear-gradient(135deg, #f7931a 0%, #ff8c00 100%)' 
      : 'rgba(247, 147, 26, 0.1)'};
  }
`;

const ErrorMessage = styled.div`
  color: #f85149;
  font-size: 12px;
  margin-top: 5px;
`;

const InfoText = styled.div`
  color: #8b949e;
  font-size: 12px;
  font-style: italic;
`;

interface QuickSelectOption {
  label: string;
  days: number;
}

const quickSelectOptions: QuickSelectOption[] = [
  { label: '1 Week', days: 7 },
  { label: '1 Month', days: 30 },
  { label: '3 Months', days: 90 },
  { label: '6 Months', days: 180 },
  { label: '1 Year', days: 365 },
  { label: '2 Years', days: 730 },
  { label: '3 Years', days: 1095 },
];

const CustomDatePicker: React.FC<CustomDateRangeProps> = ({
  onDateRangeChange,
  minDate,
  maxDate,
  defaultStartDate,
  defaultEndDate
}) => {
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [activeQuickSelect, setActiveQuickSelect] = useState<number | null>(null);

  // Format date for input
  const formatDateForInput = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  // Initialize dates
  useEffect(() => {
    const defaultStart = defaultStartDate || minDate;
    const defaultEnd = defaultEndDate || new Date(minDate.getTime() + 365 * 24 * 60 * 60 * 1000); // 1 year from start
    
    setStartDate(formatDateForInput(defaultStart));
    setEndDate(formatDateForInput(defaultEnd));
  }, [minDate, maxDate, defaultStartDate, defaultEndDate]);

  const validateDates = (start: string, end: string): string => {
    if (!start || !end) {
      return 'Please select both start and end dates';
    }

    const startDateObj = new Date(start);
    const endDateObj = new Date(end);
    const now = new Date();

    if (startDateObj < minDate) {
      return `Start date cannot be earlier than ${formatDateForInput(minDate)}`;
    }

    if (endDateObj > maxDate) {
      return `End date cannot be later than ${formatDateForInput(maxDate)}`;
    }

    if (startDateObj >= endDateObj) {
      return 'End date must be after start date';
    }

    // Check if start date is at least tomorrow
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    if (startDateObj < tomorrow) {
      return 'Start date must be at least tomorrow';
    }

    // Check maximum 3 years range
    const threeYearsFromStart = new Date(startDateObj);
    threeYearsFromStart.setFullYear(threeYearsFromStart.getFullYear() + 3);

    if (endDateObj > threeYearsFromStart) {
      return 'Date range cannot exceed 3 years';
    }

    return '';
  };

  const handleApply = () => {
    const validationError = validateDates(startDate, endDate);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError('');
    setActiveQuickSelect(null);
    onDateRangeChange({
      startDate: new Date(startDate),
      endDate: new Date(endDate)
    });
  };

  const handleQuickSelect = (option: QuickSelectOption, index: number) => {
    const start = new Date(minDate);
    const end = new Date(start.getTime() + option.days * 24 * 60 * 60 * 1000);
    
    // Ensure end date doesn't exceed max date
    const actualEndDate = end > maxDate ? maxDate : end;
    
    setStartDate(formatDateForInput(start));
    setEndDate(formatDateForInput(actualEndDate));
    setActiveQuickSelect(index);
    setError('');
  };

  const handleStartDateChange = (value: string) => {
    setStartDate(value);
    setActiveQuickSelect(null);
    setError('');
  };

  const handleEndDateChange = (value: string) => {
    setEndDate(value);
    setActiveQuickSelect(null);
    setError('');
  };

  return (
    <DatePickerContainer>
      <div>
        <Label>Quick Select</Label>
        <QuickSelectContainer>
          {quickSelectOptions.map((option, index) => (
            <QuickSelectButton
              key={option.label}
              isActive={activeQuickSelect === index}
              onClick={() => handleQuickSelect(option, index)}
            >
              {option.label}
            </QuickSelectButton>
          ))}
        </QuickSelectContainer>
      </div>
      
      <DatePickerRow>
        <DateInputGroup>
          <Label>From Date</Label>
          <DateInput
            type="date"
            value={startDate}
            min={formatDateForInput(minDate)}
            max={formatDateForInput(maxDate)}
            onChange={(e) => handleStartDateChange(e.target.value)}
          />
        </DateInputGroup>
        
        <DateInputGroup>
          <Label>To Date</Label>
          <DateInput
            type="date"
            value={endDate}
            min={formatDateForInput(minDate)}
            max={formatDateForInput(maxDate)}
            onChange={(e) => handleEndDateChange(e.target.value)}
          />
        </DateInputGroup>
        
        <ApplyButton
          onClick={handleApply}
          disabled={!startDate || !endDate}
        >
          Apply Range
        </ApplyButton>
      </DatePickerRow>
      
      {error && <ErrorMessage>{error}</ErrorMessage>}
      
      <InfoText>
        Select a date range from tomorrow up to 3 years in the future. Quick select options will automatically apply the range.
      </InfoText>
    </DatePickerContainer>
  );
};

export default CustomDatePicker; 