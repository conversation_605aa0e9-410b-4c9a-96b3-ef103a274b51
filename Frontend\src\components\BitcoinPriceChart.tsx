import React, { useState, useEffect } from 'react';
import ReactECharts from 'echarts-for-react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  ChartHeader, 
  ChartTitle, 
  TimeFrameSelector, 
  TimeFrameButton, 
  Disclaimer,
  LoadingSpinner
} from '../styles/StyledComponents';
import { fetchPredictionFromCSV, PredictionData, getDateRangeData, getCustomDateRangeData } from '../api/csvPredictionApi';
import { TimeFrame, TimeFrameMapping, ChartData, DateRange } from '../types';
import CustomDatePicker from './CustomDatePicker';

const timeFrameMap: TimeFrameMapping = {
  '1m': 30,
  '6m': 180,
  '1y': 365,
  '3y': 1095,
};

const BitcoinPriceChart: React.FC = () => {
  const [selectedTimeFrame, setSelectedTimeFrame] = useState<TimeFrame>('1m');
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [allPredictionData, setAllPredictionData] = useState<PredictionData[]>([]);
  const [customDateRange, setCustomDateRange] = useState<DateRange | null>(null);
  const [minDate, setMinDate] = useState<Date>(new Date());
  const [maxDate, setMaxDate] = useState<Date>(new Date());

  // Load all prediction data on component mount
  useEffect(() => {
    const loadAllData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        console.log('Loading prediction data from CSV...');
        const response = await fetchPredictionFromCSV();
        
        if (response && response.csv_data) {
          const data: PredictionData[] = response.csv_data.dates.map((date, index) => ({
            Date: date,
            Predicted_Price: response.csv_data.predicted_prices[index]
          }));
          
          setAllPredictionData(data);
          
          // Set date boundaries
          const firstDate = new Date(data[0].Date);
          const lastDate = new Date(data[data.length - 1].Date);
          setMinDate(firstDate);
          setMaxDate(lastDate);
          
          console.log(`Loaded ${data.length} predictions from CSV`);
          console.log(`Date range: ${firstDate.toDateString()} to ${lastDate.toDateString()}`);
        } else {
          throw new Error('Invalid data format received from CSV');
        }
      } catch (err: any) {
        console.error('Error loading CSV data:', err);
        setError(err.message || 'Failed to load prediction data. Please check if the CSV file exists.');
      } finally {
        setLoading(false);
      }
    };

    loadAllData();
  }, []);

  // Update chart data when timeframe or custom date range changes
  useEffect(() => {
    if (allPredictionData.length === 0) return;

    try {
      let data;
      
      if (selectedTimeFrame === 'custom' && customDateRange) {
        // Use custom date range
        data = getCustomDateRangeData(allPredictionData, customDateRange.startDate, customDateRange.endDate);
        const key = Object.keys(data)[0];
        setChartData({
          dates: data[key].dates,
          prices: data[key].predicted_prices,
        });
      } else if (selectedTimeFrame !== 'custom') {
        // Use predefined timeframe
        const days = timeFrameMap[selectedTimeFrame];
        data = getDateRangeData(allPredictionData, days);
        const key = `${days}_days`;
        
        if (data && data[key]) {
          setChartData({
            dates: data[key].dates,
            prices: data[key].predicted_prices,
          });
        }
      }
      
      console.log(`Chart updated for ${selectedTimeFrame}`);
    } catch (err: any) {
      console.error('Error processing chart data:', err);
      setError('Error processing chart data');
    }
  }, [selectedTimeFrame, customDateRange, allPredictionData]);

  const getOption = () => {
    if (!chartData) return {};

    return {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(13, 17, 23, 0.9)',
        borderColor: '#30363d',
        textStyle: {
          color: '#e6e8ea',
        },
        formatter: (params: any) => {
          const dataIndex = params[0].dataIndex;
          const date = chartData.dates[dataIndex];
          const price = chartData.prices[dataIndex].toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD',
          });
          return `<strong>${date}</strong><br/>Predicted Price: ${price}`;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: chartData.dates,
        axisLine: {
          lineStyle: {
            color: '#30363d',
          },
        },
        axisLabel: {
          color: '#8b949e',
          formatter: (value: string) => {
            const date = new Date(value);
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
          },
        },
        boundaryGap: false,
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#30363d',
          },
        },
        splitLine: {
          lineStyle: {
            color: '#21262d',
          },
        },
        axisLabel: {
          color: '#8b949e',
          formatter: (value: number) => {
            return '$' + value.toLocaleString();
          },
        },
        scale: true,
        min: (value: { min: number }) => {
          // Set min to ~20% below the minimum value
          return Math.floor(value.min * 0.8);
        },
        max: (value: { max: number }) => {
          // Set max to ~20% above the maximum value
          return Math.ceil(value.max * 1.2);
        },
        splitNumber: 20,
        minInterval: 500
      },
      series: [
        {
          name: 'BTC Price Prediction',
          type: 'line',
          data: chartData.prices,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            color: '#f7931a',
            width: 3,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(247, 147, 26, 0.5)',
                },
                {
                  offset: 1,
                  color: 'rgba(247, 147, 26, 0.05)',
                },
              ],
            },
          },
        },
      ],
    };
  };

  const handleTimeFrameChange = (timeFrame: TimeFrame) => {
    setSelectedTimeFrame(timeFrame);
    if (timeFrame !== 'custom') {
      setCustomDateRange(null);
    }
  };

  const handleCustomDateRangeChange = (dateRange: DateRange) => {
    setCustomDateRange(dateRange);
    setSelectedTimeFrame('custom');
  };

  const renderErrorMessage = () => {
    const isCsvError = error?.includes('CSV') || error?.includes('fetch');
    
    return (
      <div style={{ 
        height: '400px', 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center', 
        justifyContent: 'center', 
        color: '#f85149',
        textAlign: 'center',
        padding: '0 20px'
      }}>
        <div style={{ fontSize: '1.2rem', marginBottom: '10px' }}>
          {error}
        </div>
        
        {isCsvError && (
          <div style={{ 
            color: '#8b949e', 
            fontSize: '0.9rem', 
            marginTop: '20px',
            maxWidth: '600px',
            textAlign: 'left',
            background: 'rgba(255,255,255,0.05)',
            padding: '15px',
            borderRadius: '5px'
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>Troubleshooting Steps:</div>
            <ol style={{ paddingLeft: '20px', margin: 0 }}>
              <li>Make sure the AI model has generated the CSV file</li>
              <li>Run the Python script to generate predictions: <code style={{ background: '#21262d', padding: '3px 5px', borderRadius: '3px' }}>python AI/bitcoin_price_prediction_using_lstm.py</code></li>
              <li>Check if the file exists: <code style={{ background: '#21262d', padding: '3px 5px', borderRadius: '3px' }}>public/bitcoin_5year_predictions.csv</code></li>
              <li>Copy the generated CSV from Data/ to Frontend/public/ folder</li>
              <li>Refresh this page after the CSV file is available</li>
            </ol>
          </div>
        )}
        
        <button 
          onClick={() => handleTimeFrameChange(selectedTimeFrame)} 
          style={{ 
            marginTop: '15px', 
            padding: '8px 15px', 
            backgroundColor: 'var(--accent)',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    );
  };

  return (
    <ChartContainer>
      <ChartHeader>
        <ChartTitle>Bitcoin Price Prediction</ChartTitle>
        <TimeFrameSelector>
          <TimeFrameButton 
            active={selectedTimeFrame === '1m'} 
            onClick={() => handleTimeFrameChange('1m')}
          >
            1M
          </TimeFrameButton>
          <TimeFrameButton 
            active={selectedTimeFrame === '6m'} 
            onClick={() => handleTimeFrameChange('6m')}
          >
            6M
          </TimeFrameButton>
          <TimeFrameButton 
            active={selectedTimeFrame === '1y'} 
            onClick={() => handleTimeFrameChange('1y')}
          >
            1Y
          </TimeFrameButton>
          <TimeFrameButton 
            active={selectedTimeFrame === '3y'} 
            onClick={() => handleTimeFrameChange('3y')}
          >
            3Y
          </TimeFrameButton>
          <TimeFrameButton 
            active={selectedTimeFrame === 'custom'} 
            onClick={() => handleTimeFrameChange('custom')}
          >
            Custom
          </TimeFrameButton>
        </TimeFrameSelector>
      </ChartHeader>
      
      {selectedTimeFrame === 'custom' && !loading && minDate && maxDate && (
        <CustomDatePicker
          onDateRangeChange={handleCustomDateRangeChange}
          minDate={minDate}
          maxDate={maxDate}
          defaultStartDate={minDate}
          defaultEndDate={new Date(minDate.getTime() + 365 * 24 * 60 * 60 * 1000)}
        />
      )}
      
      {loading ? (
        <LoadingSpinner />
      ) : error ? (
        renderErrorMessage()
      ) : (
        <ReactECharts 
          option={getOption()} 
          style={{ height: '600px' }}
          opts={{ renderer: 'canvas' }}
          notMerge={true}
          lazyUpdate={true}
        />
      )}
      <Disclaimer>
        * This is a prediction model based on historical data and should not be used as financial advice. 
        Predictions are loaded from CSV files generated by the AI model. 
        Use custom date ranges to explore different time periods up to 3 years in the future.
      </Disclaimer>
    </ChartContainer>
  );
};

export default BitcoinPriceChart; 