import <PERSON> from 'papaparse';
import { PredictionResponse } from '../types';

export interface PredictionData {
  Date: string;
  Predicted_Price: number;
}

export const fetchPredictionFromCSV = async (startDate?: Date, endDate?: Date): Promise<PredictionResponse> => {
  try {
    console.log('Fetching predictions from CSV file...');
    
    // Fetch the CSV file from the public directory
    const response = await fetch('/bitcoin_5year_predictions.csv');
    if (!response.ok) {
      throw new Error(`Failed to fetch CSV: ${response.statusText}`);
    }
    
    const csvText = await response.text();
    
    // Parse CSV data
    const parseResult = Papa.parse<PredictionData>(csvText, {
      header: true,
      skipEmptyLines: true,
      dynamicTyping: true,
    });
    
    if (parseResult.errors.length > 0) {
      console.error('CSV parsing errors:', parseResult.errors);
      throw new Error('Failed to parse CSV data');
    }
    
    let data = parseResult.data;
    
    // Filter data based on date range if provided
    if (startDate || endDate) {
      data = data.filter(row => {
        const rowDate = new Date(row.Date);
        const isAfterStart = !startDate || rowDate >= startDate;
        const isBeforeEnd = !endDate || rowDate <= endDate;
        return isAfterStart && isBeforeEnd;
      });
    }
    
    // Convert to the expected format
    const dates = data.map(row => row.Date);
    const predicted_prices = data.map(row => row.Predicted_Price);
    
    console.log(`Loaded ${data.length} predictions from CSV`);
    
    return {
      'csv_data': {
        dates,
        predicted_prices,
      }
    };
  } catch (error: any) {
    console.error('Error loading CSV prediction data:', error);
    throw new Error(`Failed to load prediction data: ${error.message}`);
  }
};

export const getDateRangeData = (data: PredictionData[], days: number): PredictionResponse => {
  // Get the first N days from the data
  const filteredData = data.slice(0, days);
  
  return {
    [`${days}_days`]: {
      dates: filteredData.map(row => row.Date),
      predicted_prices: filteredData.map(row => row.Predicted_Price),
    }
  };
};

export const getCustomDateRangeData = (data: PredictionData[], startDate: Date, endDate: Date): PredictionResponse => {
  // Filter data for custom date range
  const filteredData = data.filter(row => {
    const rowDate = new Date(row.Date);
    return rowDate >= startDate && rowDate <= endDate;
  });
  
  const days = filteredData.length;
  
  return {
    [`custom_${days}_days`]: {
      dates: filteredData.map(row => row.Date),
      predicted_prices: filteredData.map(row => row.Predicted_Price),
    }
  };
}; 